package com.navigator.cuckoo.service;

import com.google.gson.Gson;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jms.core.JmsTemplate;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

// 000000 test-延时投递消息 changed by <PERSON> at 2025-1-7 start
/**
 * 定时消息投递服务
 * 使用JMS和ScheduledExecutorService实现定时消息投递功能
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ScheduledMessageService {

    private final JmsTemplate jmsTemplate;
    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(10);
    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    
    /**
     * 发送定时消息
     *
     * @param queueName 队列名称
     * @param messageData 消息数据
     * @param scheduledTime 定时投递时间，格式：yyyy-MM-dd HH:mm:ss，为空则立即投递
     */
    public void sendScheduledMessage(String queueName, Object messageData, String scheduledTime) {
        try {
            String messageBody = new Gson().toJson(messageData);

            if (scheduledTime != null && !scheduledTime.trim().isEmpty()) {
                // 解析定时投递时间
                LocalDateTime targetTime = LocalDateTime.parse(scheduledTime, DATE_TIME_FORMATTER);
                LocalDateTime now = LocalDateTime.now();

                // 检查时间是否在未来
                if (targetTime.isBefore(now)) {
                    log.warn("定时投递时间{}已过期，将立即投递消息", scheduledTime);
                    jmsTemplate.convertAndSend(queueName, messageBody);
                } else {
                    // 计算延时时间
                    long delaySeconds = ChronoUnit.SECONDS.between(now, targetTime);

                    // 使用ScheduledExecutorService定时投递
                    scheduler.schedule(() -> {
                        try {
                            jmsTemplate.convertAndSend(queueName, messageBody);
                            log.info("定时消息已投递到队列: {}, 内容: {}", queueName, messageBody);
                        } catch (Exception e) {
                            log.error("定时投递消息失败: {}", e.getMessage(), e);
                        }
                    }, delaySeconds, TimeUnit.SECONDS);

                    log.info("消息已安排在{}定时投递到队列: {}, 延时{}秒", scheduledTime, queueName, delaySeconds);
                }
            } else {
                // 立即投递
                jmsTemplate.convertAndSend(queueName, messageBody);
                log.info("消息已立即投递到队列: {}", queueName);
            }

        } catch (Exception e) {
            log.error("发送定时消息失败: {}", e.getMessage(), e);
            throw new RuntimeException("发送定时消息失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 创建合同查询消息数据
     */
    public Map<String, Object> createContractQueryMessage(String businessEntity, String contractCode, String action) {
        Map<String, Object> messageData = new HashMap<>();
        messageData.put("action", action);
        messageData.put("businessEntity", businessEntity);
        messageData.put("contractCode", contractCode);
        messageData.put("timestamp", System.currentTimeMillis());
        return messageData;
    }
}
// 000000 test-延时投递消息 changed by Jason Shi at 2025-1-7 end
