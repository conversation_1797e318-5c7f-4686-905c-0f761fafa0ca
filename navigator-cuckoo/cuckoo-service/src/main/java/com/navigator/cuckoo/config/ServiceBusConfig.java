package com.navigator.cuckoo.config;

import com.azure.messaging.servicebus.ServiceBusClientBuilder;
import com.azure.messaging.servicebus.ServiceBusSenderClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Azure Service Bus 配置类
 * 
 * <AUTHOR>
 * @since 2025-1-7
 */
@Slf4j
@Configuration
public class ServiceBusConfig {

    @Value("${spring.jms.servicebus.connection-string}")
    private String connectionString;

    @Value("${messageQueue.atlas.syncQueueName}")
    private String atlasQueueName;

    /**
     * 创建 Service Bus Sender Client
     */
    @Bean
    public ServiceBusSenderClient serviceBusSenderClient() {
        log.info("初始化 Azure Service Bus Sender Client，队列: {}", atlasQueueName);
        
        return new ServiceBusClientBuilder()
                .connectionString(connectionString)
                .sender()
                .queueName(atlasQueueName)
                .buildClient();
    }
}
