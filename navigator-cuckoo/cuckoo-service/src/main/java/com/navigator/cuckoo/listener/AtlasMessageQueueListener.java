package com.navigator.cuckoo.listener;

import com.google.gson.Gson;
import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.redis.RedisUtil;
import com.navigator.cuckoo.dao.AtlasSyncRecordDao;
import com.navigator.cuckoo.dao.AtlasSyncRequestDao;
import com.navigator.cuckoo.pojo.dto.AtlasSyncUriDTO;
import com.navigator.cuckoo.pojo.dto.create.AtlasContractCreateSyncDTO;
import com.navigator.cuckoo.pojo.entity.AtlasSyncRecordEntity;
import com.navigator.cuckoo.pojo.entity.AtlasSyncRequestEntity;
import com.navigator.cuckoo.pojo.enums.AtlasOperationSourceEnum;
import com.navigator.cuckoo.pojo.enums.AtlasSyncActionEnum;
import com.navigator.cuckoo.service.IAtlasOperationLogService;
import com.navigator.cuckoo.service.IAtlasSyncCallbackService;
import com.navigator.cuckoo.service.convert.AtlasSyncUriService;
import feign.RetryableException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.jms.annotation.JmsListener;
import org.springframework.stereotype.Service;

// 000000 test-延时投递消息 changed by Jason Shi at 2025-1-7 start
import java.math.BigDecimal;
import java.util.Map;
// 000000 test-延时投递消息 changed by Jason Shi at 2025-1-7 end

/**
 * <p>
 * ATLAS 消息队列监听
 * </p>
 *
 * <AUTHOR>
 * @since 2023/5/18
 */

@Slf4j
@Service
@RequiredArgsConstructor
public class AtlasMessageQueueListener {

    @Value("${messageQueue.atlas.syncQueueName}")
    private String syncQueueName;
    @Value("${messageQueue.atlas.syncDeadLetterName}")
    private String syncDeadLetterName;

    // dao
    private final AtlasSyncRecordDao syncRecordDao;
    private final AtlasSyncRequestDao syncRequestDao;

    // service
    private final AtlasSyncUriService uriService;
    private final IAtlasSyncCallbackService syncCallbackService;
    private final IAtlasOperationLogService operationLogService;

    private final RedisUtil redisUtil;

    /**
     * 同步ATLAS监听
     */
    @JmsListener(destination = "${messageQueue.atlas.syncQueueName}", containerFactory = "jmsListenerContainerFactory")
    public void receiveQueueMessage(String message) {
        log.info("===============[{}] Queue Message received : {}===============", syncQueueName, message);

        try {
            // 000000 test-延时投递消息 changed by Jason Shi at 2025-1-7 start
            // 检查是否是定时查询消息
            if (message.contains("\"action\":\"getContractOpenQuantity\"")) {
                handleScheduledQueryMessage(message);
                return;
            }
            // 000000 test-延时投递消息 changed by Jason Shi at 2025-1-7 end

            AtlasContractCreateSyncDTO contractCreateSyncDTO = new Gson().fromJson(message, AtlasContractCreateSyncDTO.class);

            // 根据optionType获取不同的uuid
            String uuid = getSyncUuid(contractCreateSyncDTO);

            AtlasSyncRecordEntity recordEntity = syncRecordDao.getByUuid(uuid);

            if (null == recordEntity) {
                log.error("method: receiveQueueMessage, sync record is not exist, uuid: {}", uuid);
                return;
            }

            // 修改接口的有序性，防止旧记录覆盖新记录
            try {
                if (recordEntity.getOperationType().equals(AtlasSyncActionEnum.MODIFY.getValue())) {
                    // 获取该请求的requestId
                    AtlasSyncRequestEntity syncRequest = syncRequestDao.getById(recordEntity.getRequestId());
                    // 查询最新的requestId
                    AtlasSyncRequestEntity latestSyncRequest = syncRequestDao.getLatestByBizCode(syncRequest.getBizCode());

                    if (null != latestSyncRequest && latestSyncRequest.getId() > syncRequest.getId()) {
                        log.error("method: receiveQueueMessage, sync modify is not latest, uuid: {}", uuid);
                        return;
                    }
                }
            } catch (Exception e) {
                log.error("method: receiveQueueMessage, sync modify is not latest,uuid: {}", uuid);
                return;
            }

            // 同步接口
            AtlasSyncUriDTO syncUriDTO = new AtlasSyncUriDTO()
                    .setContractDefDTO(contractCreateSyncDTO.getAtlasContractDefDTO())
                    .setDeliveryDefDTO(contractCreateSyncDTO.getAtlasDeliveryDefDTO())
                    .setTradeType(contractCreateSyncDTO.getTradeType())
                    .setOperationType(contractCreateSyncDTO.getOptionType());
            ResponseEntity<String> responseEntity = uriService.syncAtlasInfo(syncUriDTO);

            // 处理回调
            syncCallbackService.processSyncCallBack(recordEntity, responseEntity);

            // 记录日志
            String optionSource = AtlasOperationSourceEnum.TRADE_SERVICE_MESSAGE_CALL.getDesc();
            if (StringUtils.isNotBlank(contractCreateSyncDTO.getOptionSource())) {
                optionSource = optionSource + "|" + contractCreateSyncDTO.getOptionSource();
            }

            operationLogService.saveOperationLogByRecord(recordEntity.setRequestUri(syncUriDTO.getRequestUri()), optionSource);
        } catch (RetryableException e) {
            // 只有网络异常才会重试
            log.error("method: receiveQueueMessage, send ATLAS syncData error,retry.......: {}", e.getMessage());

            try {
                AtlasContractCreateSyncDTO contractCreateSyncDTO = new Gson().fromJson(message, AtlasContractCreateSyncDTO.class);
                // send error
                String uuid = getSyncUuid(contractCreateSyncDTO);

                AtlasSyncRecordEntity recordEntity = syncRecordDao.getByUuid(uuid);

                // 记录日志
                String optionSource = AtlasOperationSourceEnum.TRADE_SERVICE_MESSAGE_CALL.getDesc();
                if (StringUtils.isNotBlank(contractCreateSyncDTO.getOptionSource())) {
                    optionSource = optionSource + "|" + contractCreateSyncDTO.getOptionSource();
                }

                int tryTimes = 0;

                if (null != redisUtil.get("atlas:contract:uuid:" + uuid)) {
                    tryTimes = Math.toIntExact(redisUtil.incr("atlas:contract:uuid:" + uuid, 1L));
                    optionSource = AtlasOperationSourceEnum.INTERFACE_AUTO_RETRY.getDesc();

                } else {
                    redisUtil.set("atlas:contract:uuid:" + uuid, 0);
                }

                // 增加重试次数
                recordEntity.setTryTimes(tryTimes);

                // 处理同步异常回调
                syncCallbackService.processSyncCallBackError(recordEntity);

                // atlas返回的信息存放异常原因
                recordEntity.setAtlasResultsInfo(e.getMessage());

                operationLogService.saveOperationLogByRecord(recordEntity, optionSource);
            } catch (Exception exception) {
                // 抛异常把自己抛挂了
                log.error("The throwing anomaly threw itself up: {}", exception.getMessage());
            }

            // 显式抛出异常：需要进行消息重试
            throw new BusinessException(ResultCodeEnum.SYNC_ATLAS_CONTRACT_EXCEPTION, e.getMessage());

        } catch (Exception e) {
            // 业务异常不进行重试
            log.error("method: receiveQueueMessage, send ATLAS syncData error: {}", e.getMessage());
        }
    }

    /**
     * 根据optionType获取不同的uuid
     *
     * @param contractCreateSyncDTO 同步数据
     * @return
     */
    private static String getSyncUuid(AtlasContractCreateSyncDTO contractCreateSyncDTO) {
        String uuid = "";
        // BUGFIX：case-1003129 DR取消接口无法正常传输 Author: Mr 2025-04-11 start
        if (AtlasSyncActionEnum.DELIVERY_REQUEST.getValue().equals(contractCreateSyncDTO.getOptionType())
                || AtlasSyncActionEnum.DELIVERY_REQUEST_CANCEL.getValue().equals(contractCreateSyncDTO.getOptionType())) {
            // BUGFIX：case-1003129 DR取消接口无法正常传输 Author: Mr 2025-04-11 end
            // 提货获取uuid
            uuid = contractCreateSyncDTO.getAtlasDeliveryDefDTO().getDeliveryRequest().getHeader().getUuid();
        } else {
            // 合同获取uuid
            uuid = contractCreateSyncDTO.getAtlasContractDefDTO().getContract().getHeader().getUuid();
        }
        return uuid;
    }

    /**
     * 同步ATLAS死信队列监听
     *
     * @param message
     */
    @JmsListener(destination = "${messageQueue.atlas.syncDeadLetterName}", containerFactory = "jmsListenerContainerFactory")
    public void receiveDLQueueMessage(String message) {
        log.info("===============[{}] DLQ Message received : {}===============", syncDeadLetterName, message);
    }

    // 000000 test-延时投递消息 changed by Jason Shi at 2025-1-7 start
    /**
     * 处理定时查询消息
     */
    private void handleScheduledQueryMessage(String message) {
        try {
            log.info("收到定时合同查询消息: {}", message);

            // 解析消息内容
            @SuppressWarnings("unchecked")
            Map<String, Object> messageData = new Gson().fromJson(message, Map.class);

            String action = (String) messageData.get("action");
            String businessEntity = (String) messageData.get("businessEntity");
            String contractCode = (String) messageData.get("contractCode");

            if ("getContractOpenQuantity".equals(action)) {
                // 执行合同开放数量查询
                BigDecimal result = uriService.getContractOpenQuantity(businessEntity, contractCode);
                log.info("定时查询合同openQuantity结果: {}, 合同: {}, 账套: {}",
                        result, contractCode, businessEntity);
            } else {
                log.warn("未知的消息动作: {}", action);
            }

        } catch (Exception e) {
            log.error("处理定时查询消息时发生错误: {}", e.getMessage(), e);
        }
    }
    // 000000 test-延时投递消息 changed by Jason Shi at 2025-1-7 end
}
