package com.navigator.future.kingstar.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.navigator.admin.facade.CompanyFacade;
import com.navigator.admin.pojo.entity.CompanyEntity;
import com.navigator.bisiness.enums.PriceTypeEnum;
import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.common.redis.RedisUtil;
import com.navigator.common.util.BigDecimalUtil;
import com.navigator.common.util.JwtUtils;
import com.navigator.future.dao.PriceApplyDao;
import com.navigator.future.enums.*;
import com.navigator.future.kingstar.dao.KingStarSyncCallbackDao;
import com.navigator.future.kingstar.dao.KingStarSyncRequestDao;
import com.navigator.future.kingstar.remote.FutureRemoteFacade;
import com.navigator.future.kingstar.service.IKingStarSyncService;
import com.navigator.future.kingstar.service.KingStarSyncUriService;
import com.navigator.future.kingstar.util.AESUtil;
import com.navigator.future.pojo.dto.NotDealDTO;
import com.navigator.future.pojo.dto.PriceApplyDTO;
import com.navigator.future.pojo.dto.kingstar.KingStarCallbackDTO;
import com.navigator.future.pojo.dto.kingstar.KingStarRequestDTO;
import com.navigator.future.pojo.dto.kingstar.KingStarResponseDTO;
import com.navigator.future.pojo.entity.PriceApplyEntity;
import com.navigator.future.pojo.entity.PriceApplyLogEntity;
import com.navigator.future.pojo.entity.kingstar.KingStarSyncCallbackEntity;
import com.navigator.future.pojo.entity.kingstar.KingStarSyncRequestEntity;
import com.navigator.future.service.IPriceApplyService;
import com.navigator.goods.facade.CategoryFacade;
import com.navigator.goods.pojo.entity.CategoryEntity;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.*;

/**
 * <p>
 * 同步接口的实现
 * </p>
 *
 * <AUTHOR>
 * @since 2025/06/05
 */
@Slf4j
@Service
@RefreshScope
@RequiredArgsConstructor
public class KingStarSyncServiceImpl implements IKingStarSyncService {

    @Value("${kingstar.syncStatus}")
    private Integer syncStatus;

    @Resource
    private RedisUtil redisUtil;
    @Resource
    private KingStarSyncUriService syncUriService;
    @Resource
    private KingStarSyncRequestDao kingStarSyncRequestDao;
    @Resource
    private KingStarSyncCallbackDao kingStarSyncCallbackDao;
    @Resource
    private PriceApplyDao priceApplyDao;
    @Resource
    private IPriceApplyService priceApplyService;
    @Resource
    private FutureRemoteFacade futureRemoteFacade;
    @Resource
    private CompanyFacade companyFacade;
    @Resource
    private CategoryFacade categoryFacade;

    @Override
    public void syncPriceApplyInfo(PriceApplyEntity priceApplyEntity) {

        // 检查同步状态
        if (syncStatus == null || syncStatus == 0) {
            log.info("【KingStar】同步状态未开启，跳过同步挂单申请，申请单号: {}", priceApplyEntity.getCode());
            return;
        }

        // 1. 状态判断
        if (priceApplyEntity.getStatus() == PriceStatusEnum.PRICING.getValue()) {
            log.info("当前申请单状态为点价中，不触发同步接口，申请单号: {}", priceApplyEntity.getCode());
            return;
        }

        // 2. 获取用户信息
        String userCode = "admin"; // TODO: 与KingStar对接确认userCode获取方式

        // 3. 准备请求数据
        int businessType;
        try {
            businessType = getBusinessType(priceApplyEntity);
        } catch (Exception e) {
            log.error("获取业务类型失败，申请单号: {}, 错误信息: {}", priceApplyEntity.getCode(), e.getMessage(), e);
            return;
        }

        boolean isReversePricing =
                businessType == KingStarBusinessTypeEnum.REVERSE_PRICING.getValue()
                        || businessType == KingStarBusinessTypeEnum.REVERSE_PRICING_TRANSFER.getValue();
        KingStarRequestDTO requestDTO = genKingStarRequestDTO(priceApplyEntity, businessType);
        KingStarSyncRequestEntity syncRequest = genSyncRequest(userCode, requestDTO, priceApplyEntity);

        // 4. 调用接口
        String pendingResult = "";
        String response = "";
        try {
//            KingStarResponseDTO responseDTO = isReversePricing
//                    ? syncUriService.applyReversePricing(userCode, requestDTO, syncRequest)
//                    : syncUriService.applyPricing(userCode, requestDTO, syncRequest);
            // TODO:mock {"code":200,"data":"指令下单成功！","message":"成功"}
            KingStarResponseDTO responseDTO = KingStarResponseDTO.builder()
                    .code(200)
                    .message("成功")
                    .data("指令下单成功！")
                    .build();

            pendingResult = (responseDTO == null) ? "接口调用失败" : responseDTO.getMessage();

            response = JSONUtil.toJsonStr(responseDTO);

        } catch (Exception e) {
            pendingResult = "接口调用失败";
            response = "同步挂单失败" + e.getMessage();
            log.error("同步挂单申请失败，申请单号: {}, 错误信息: {}", priceApplyEntity.getCode(), e.getMessage(), e);
        }

        // 5. 更新申请单状态
        updatePriceApplyEntity(priceApplyEntity, pendingResult);

        // 6. 保存同步记录
        if (syncRequest != null) {
            syncRequest.setSyncStatus(pendingResult)
                    .setResponseInfo(response);
            kingStarSyncRequestDao.save(syncRequest);
        }

        log.info("KingStar请求记录已保存，指令ID: {}", requestDTO.getInstructId());
    }

    private void updatePriceApplyEntity(PriceApplyEntity entity, String pendingResult) {
        entity.setStatus(pendingResult.equals("成功") ? PriceStatusEnum.WAIT_PENDING.getValue() : PriceStatusEnum.PRICING.getValue())
                .setInterfaceStatus(pendingResult.equals("成功") ? KingStarInterfaceStatusEnum.PENDING.getValue() : KingStarInterfaceStatusEnum.NOT_CALL_INTERFACE.getValue())
                .setPendingResult(pendingResult)
                .setUpdatedAt(new Date());
        priceApplyDao.updateById(entity);
    }

    /**
     * 获取业务类型
     *
     * @param priceApplyEntity 申请实体
     * @return 业务类型值
     */
    private int getBusinessType(PriceApplyEntity priceApplyEntity) {

        PriceTypeEnum priceType = PriceTypeEnum.getByValue(priceApplyEntity.getType());
        switch (priceType) {
            case PRICING:
                return KingStarBusinessTypeEnum.PRICING.getValue();
            case TRANSFER_MONTH:
                return KingStarBusinessTypeEnum.PRICING_TRANSFER.getValue();
            case REVERSE_PRICING:
                boolean isSame = ObjectUtil.equal(
                        priceApplyEntity.getDominantCode(),
                        priceApplyEntity.getTranferDominantCode()
                );
                return isSame
                        ? KingStarBusinessTypeEnum.REVERSE_PRICING.getValue()
                        : KingStarBusinessTypeEnum.REVERSE_PRICING_TRANSFER.getValue();
            default:
                throw new IllegalArgumentException("Unsupported price type: " + priceApplyEntity.getType());
        }
    }

    /**
     * 保存同步请求记录
     *
     * @param userCode           KingStar请求用户code
     * @param kingStarRequestDTO KingStar请求DTO
     * @param priceApplyEntity   申请实体
     */
    private KingStarSyncRequestEntity genSyncRequest(String userCode, KingStarRequestDTO kingStarRequestDTO, PriceApplyEntity priceApplyEntity) {
        if (kingStarRequestDTO == null || StringUtils.isBlank(kingStarRequestDTO.getInstructId())) {
            log.error("KingStar请求DTO或指令ID为空，无法保存同步请求记录");
            return null;
        }

        // 将请求记录保存到数据库
        KingStarSyncRequestEntity syncRequestEntity = new KingStarSyncRequestEntity();
        return syncRequestEntity.setInstructId(kingStarRequestDTO.getInstructId())
                .setBizId(priceApplyEntity.getId())
                .setBizCode(priceApplyEntity.getCode())
                .setBizType(priceApplyEntity.getType())
                .setOperationType(kingStarRequestDTO.getType())
                .setSyncTime(kingStarRequestDTO.getTimestamp())
                .setUserCode(userCode)
                .setRequestInfo(JSONUtil.toJsonStr(kingStarRequestDTO))
                .setCreatedBy(kingStarRequestDTO.getApplyUserName())
                .setUpdatedBy(kingStarRequestDTO.getApplyUserName())
                .setTryTimes(0)
                .setCreatedAt(new Date())
                .setUpdatedAt(new Date());
    }

    /**
     * 生成 KingStar 请求 DTO
     *
     * @param priceApplyEntity 申请实体
     * @param type             业务类型
     * @return KingStarRequestDTO
     */
    private KingStarRequestDTO genKingStarRequestDTO(PriceApplyEntity priceApplyEntity, Integer type) {

        // 构建基础的 KingStar 请求 DTO 对象
        KingStarRequestDTO kingStarRequestDTO = buildBaseKingStarRequestDTO(priceApplyEntity, type);

        List<KingStarRequestDTO.InstructStk> instructStkList = new ArrayList<>();

        // 开平方向: 自动开平
        String kpType = String.valueOf(KingStarPositionActionEnum.AUTO.getValue());

        priceApplyEntity.setPositionAction(kpType);

        KingStarBusinessTypeEnum businessType = KingStarBusinessTypeEnum.getByValue(type);
        if (businessType == null) {
            return kingStarRequestDTO;
        }

        // 组装指令集合
        String baseCode = (priceApplyEntity.getFutureCode() == null ? "" : getKingStarDomainCode(priceApplyEntity.getFutureCode(), priceApplyEntity.getDominantCode()));
        String transferCode = (priceApplyEntity.getTranferFutureCode() == null ? "" : getKingStarDomainCode(priceApplyEntity.getTranferFutureCode(), priceApplyEntity.getTranferDominantCode()));
        BigDecimal price = priceApplyEntity.getApplyPrice();
        int amt = priceApplyEntity.getApplyHandNum().intValue();

        // 个卷价格类型 1：指定价 3：市价
        String priceType = (priceApplyEntity.getPendingType() == PendingTypeEnum.FOLLOW_LARGE_CAP.getValue()) ? "3" : "1";

        switch (businessType) {
            case PRICING:
                instructStkList.add(buildInstructStk(baseCode, price, amt, "0", kpType, priceType));
                break;

            case PRICING_TRANSFER:
                // 正转月是买 逆转月是卖
                String bsType = compareCode(priceApplyEntity.getDominantCode(), priceApplyEntity.getTranferDominantCode()) ? "0" : "1";
                String stkCode = getStkFutureCode(priceApplyEntity.getFutureCode()) + " " + (bsType.equals("0") ? baseCode + "&" + transferCode : transferCode + "&" + baseCode);
                price = priceApplyEntity.getApplyDiffPrice();

                instructStkList.add(buildInstructStk(stkCode, price, amt, bsType, kpType, priceType));
                break;

            case REVERSE_PRICING:
                instructStkList.add(buildInstructStk(baseCode, price, amt, "1", kpType, priceType));
                break;

            case REVERSE_PRICING_TRANSFER:
                instructStkList.add(buildInstructStk(transferCode, price, amt, "1", kpType, priceType));
                break;

            case MODIFY:
                // 点转反的改单
                amt = priceApplyEntity.getChangeHandNum().intValue();
                price = priceApplyEntity.getType() == PriceTypeEnum.TRANSFER_MONTH.getValue() ? priceApplyEntity.getChangeDiffPrice() : priceApplyEntity.getChangePrice();


                instructStkList.add(buildInstructStk(baseCode, price, amt, null, null, priceType));
                break;
            default:
                break;
        }

        // 设置指令集合到请求DTO
        if (CollectionUtil.isNotEmpty(instructStkList)) {
            kingStarRequestDTO.setInstructStkList(instructStkList);
        }
        return kingStarRequestDTO;
    }

    private KingStarRequestDTO genKingStarModifyRequestDTO(PriceApplyEntity priceApplyEntity, PriceApplyLogEntity priceApplyLogEntity) {
        // 构建基础的 KingStar 请求 DTO 对象
        KingStarRequestDTO kingStarRequestDTO = buildBaseKingStarRequestDTO(priceApplyEntity, KingStarBusinessTypeEnum.MODIFY.getValue());

        List<KingStarRequestDTO.InstructStk> instructStkList = new ArrayList<>();

        // 开平方向: 自动开平
        String kpType = String.valueOf(KingStarPositionActionEnum.AUTO.getValue());

        priceApplyEntity.setPositionAction(kpType);

        // 组装指令集合
        String baseCode = (priceApplyEntity.getFutureCode() == null ? "" : getKingStarDomainCode(priceApplyEntity.getFutureCode(), priceApplyEntity.getDominantCode()));
        String transferCode = (priceApplyEntity.getTranferFutureCode() == null ? "" : getKingStarDomainCode(priceApplyEntity.getTranferFutureCode(), priceApplyEntity.getTranferDominantCode()));
        BigDecimal price = priceApplyLogEntity.getChangePrice();
        int amt = priceApplyLogEntity.getChangeHandNum().intValue();

        // 个卷价格类型 1：指定价 3：市价
        String priceType = (priceApplyLogEntity.getPendingType() == PendingTypeEnum.FOLLOW_LARGE_CAP.getValue()) ? "3" : "1";


        switch (PriceTypeEnum.getByValue(priceApplyEntity.getType())) {
            case PRICING:
                instructStkList.add(buildInstructStk(baseCode, price, amt, null, null, priceType));
                break;

            case TRANSFER_MONTH:
                // 正转月是买 逆转月是卖
                String bsType = compareCode(priceApplyEntity.getDominantCode(), priceApplyEntity.getTranferDominantCode()) ? "0" : "1";
                String stkCode = getStkFutureCode(priceApplyEntity.getFutureCode()) + " " + (bsType.equals("0") ? baseCode + "&" + transferCode : transferCode + "&" + baseCode);
                price = priceApplyLogEntity.getChangeDiffPrice();

                instructStkList.add(buildInstructStk(stkCode, price, amt, null, null, priceType));
                break;

            case REVERSE_PRICING:
                instructStkList.add(buildInstructStk(baseCode, price, amt, null, null, priceType));
                break;
            default:
                break;
        }

        // 设置指令集合到请求DTO
        if (CollectionUtil.isNotEmpty(instructStkList)) {
            kingStarRequestDTO.setInstructStkList(instructStkList);
        }
        return kingStarRequestDTO;
    }

    /**
     * 构建基础的 KingStar 请求 DTO 对象
     *
     * @param priceApplyEntity 申请实体
     * @param type             业务类型
     * @return KingStarRequestDTO
     */
    private KingStarRequestDTO buildBaseKingStarRequestDTO(PriceApplyEntity priceApplyEntity, Integer type) {
        KingStarRequestDTO dto = new KingStarRequestDTO();
        try {
            // 公司主体
            CompanyEntity companyEntity = companyFacade.queryCompanyById(priceApplyEntity.getCompanyId());
            if (companyEntity == null) {
                log.error("未找到对应的公司信息，companyId: {}", priceApplyEntity.getCompanyId());
                return dto;
            }

            // 货品品类
            CategoryEntity categoryEntity = categoryFacade.getBasicCategoryBySerialNo(Integer.valueOf(priceApplyEntity.getCategory2()));
            if (categoryEntity == null) {
                log.error("未找到对应的货品品类信息，category2: {}", priceApplyEntity.getCategory2());
                return dto;
            }

            // 构建 DTO
            dto.setInstructId(priceApplyEntity.getCode())
                    .setApplyUserId(JwtUtils.getCurrentUserId())
                    .setApplyUserName(priceApplyEntity.getCreatedBy())
                    .setAccount("1220576") // TODO: HMS系统配置映射
                    .setType(type)
                    .setTimestamp(DateUtil.format(new Date(), "yyyyMMddHHmmss"))
                    .setUuId(IdUtil.simpleUUID())
                    .setEntity(companyEntity.getShortName())
                    .setProduct(categoryEntity.getName());
        } catch (Exception e) {
            log.error("构建 KingStarRequestDTO 失败，申请单号: {}, 错误信息: {}", priceApplyEntity.getCode(), e.getMessage(), e);
        }
        return dto;
    }

    /**
     * 构建单个 InstructStk 对象
     */
    private KingStarRequestDTO.InstructStk buildInstructStk(String stkCode, BigDecimal price, int amt, String bsType, String kpType, String priceType) {
        return new KingStarRequestDTO.InstructStk()
                .setStkCode(stkCode)
                // 随盘价格传0
                .setPrice(Objects.equals(priceType, "3") ? BigDecimal.ZERO : price)
                .setAmt(amt)
                .setBsType(bsType)
                .setKpType(kpType)
                .setPriceType(priceType);
    }

    /**
     * 判断 dominantCode 是否小于 transferDominantCode
     */
    private boolean compareCode(String dominantCode, String transferDominantCode) {
        try {
            return Integer.parseInt(dominantCode) < Integer.parseInt(transferDominantCode);
        } catch (NumberFormatException e) {
            // 处理非法数字格式
            return false;
        }
    }

    @Override
    public void syncCancelPriceApplyInfo(PriceApplyEntity priceApplyEntity) {
        // 检查同步状态
        if (syncStatus == null || syncStatus == 0) {
            log.info("【KingStar】同步状态未开启，跳过同步撤单申请，申请单号: {}", priceApplyEntity.getCode());
            return;
        }

        // 获取用户的code TODO userCode询问KingStar对接人员
        String userCode = "admin";

        // 创建KingStar请求DTO
        KingStarRequestDTO kingStarRequestDTO = genKingStarRequestDTO(priceApplyEntity, KingStarBusinessTypeEnum.CANCEL.getValue());

        // 保存请求数据
        KingStarSyncRequestEntity syncRequestEntity = genSyncRequest(userCode, kingStarRequestDTO, priceApplyEntity);

        // 调用接口
        String pengdingResult = "";
        String response = "";
        try {
            KingStarResponseDTO responseDTO = syncUriService.applyCancel(userCode, kingStarRequestDTO, syncRequestEntity);
            pengdingResult = (responseDTO == null) ? "接口调用失败" : responseDTO.getMessage();
            response = JSONUtil.toJsonStr(responseDTO);
        } catch (Exception e) {
            pengdingResult = "接口调用失败";
            response = "同步撤单失败" + e.getMessage();
            log.error("同步撤单申请失败，申请单号: {}, 错误信息: {}", priceApplyEntity.getCode(), e.getMessage(), e);
        }

        // 更新申请单信息
        priceApplyEntity
                .setInterfaceStatus(KingStarInterfaceStatusEnum.CANCELING.getValue())
                .setChangeResult(pengdingResult)
                .setUpdatedAt(new Date());
        priceApplyDao.updateById(priceApplyEntity);

        // 保存请求记录
        if (syncRequestEntity != null) {
            syncRequestEntity.setSyncStatus(pengdingResult)
                    .setResponseInfo(response);
            kingStarSyncRequestDao.save(syncRequestEntity);
            log.info("KingStar撤单请求记录已保存，指令ID: {}", kingStarRequestDTO.getInstructId());
        } else {
            log.error("KingStar撤单请求记录保存失败，syncRequestEntity为null");
        }
    }

    @Override
    public void syncModifyPriceApplyInfo(PriceApplyLogEntity priceApplyLogEntity) {
        // 获取申请单信息
        PriceApplyEntity priceApplyEntity = priceApplyDao.getPriceApplyByCode(priceApplyLogEntity.getPriceApplyCode());
        if (priceApplyEntity == null) {
            log.error("未找到申请单信息，申请单号: {}", priceApplyLogEntity.getPriceApplyCode());
            return;
        }

        // 检查同步状态
        if (syncStatus == null || syncStatus == 0) {
            log.info("【KingStar】同步状态未开启，跳过同步改单申请，申请单号: {}", priceApplyEntity.getCode());
            return;
        }

        // 获取用户的code TODO userCode询问KingStar对接人员
        String userCode = "admin";

        // 创建KingStar请求DTO
        KingStarRequestDTO kingStarRequestDTO = genKingStarModifyRequestDTO(priceApplyEntity, priceApplyLogEntity);

        // 保存请求数据
        KingStarSyncRequestEntity syncRequestEntity = genSyncRequest(userCode, kingStarRequestDTO, priceApplyEntity);

        // 调用接口
        String pengdingResult = "";
        String response = "";
        try {
            KingStarResponseDTO responseDTO = syncUriService.applyModify(userCode, kingStarRequestDTO, syncRequestEntity);
            pengdingResult = (responseDTO == null) ? "接口调用失败" : responseDTO.getMessage();
            response = JSONUtil.toJsonStr(responseDTO);
        } catch (Exception e) {
            pengdingResult = "接口调用失败";
            response = "同步改单失败" + e.getMessage();
            log.error("同步改单申请失败，申请单号: {}, 错误信息: {}", priceApplyEntity.getCode(), e.getMessage(), e);
        }

        // 更新申请单信息
        priceApplyEntity
                .setInterfaceStatus(KingStarInterfaceStatusEnum.MODIFYING.getValue())
                .setChangeResult(pengdingResult)
                .setUpdatedAt(new Date());
        priceApplyDao.updateById(priceApplyEntity);

        // 保存请求记录
        if (syncRequestEntity != null) {
            syncRequestEntity.setSyncStatus(pengdingResult)
                    .setResponseInfo(response);
            kingStarSyncRequestDao.save(syncRequestEntity);
            log.info("KingStar改单请求记录已保存，指令ID: {}", kingStarRequestDTO.getInstructId());
        } else {
            log.error("KingStar改单请求记录保存失败，syncRequestEntity为null");
        }
    }

    @Override
    public KingStarResponseDTO handleInstructCallback(String kingStarCallbackDTO) {
        log.info("【KingStar】处理回调指令，原始回调数据: {}", kingStarCallbackDTO);

        if (StringUtils.isBlank(kingStarCallbackDTO)) {
            log.warn("【KingStar】回调数据为空");
            return KingStarResponseDTO.failure(ResultCodeEnum.KINGSTAR_REQUEST_PARAM_ERROR);
        }

        KingStarCallbackDTO callbackDTO;
        try {
            callbackDTO = JSON.parseObject(kingStarCallbackDTO, KingStarCallbackDTO.class);
        } catch (Exception e) {
            log.error("【KingStar】回调数据解析失败，错误信息: {}", e.getMessage(), e);
            return KingStarResponseDTO.failure(ResultCodeEnum.KINGSTAR_REQUEST_PARAM_ERROR);
        }

        if (callbackDTO == null || StringUtils.isBlank(callbackDTO.getInstructId())) {
            log.error("【KingStar】回调数据不完整：callbackDTO 或 instructId 为空");
            return KingStarResponseDTO.failure(ResultCodeEnum.KINGSTAR_REQUEST_DATA_NOT_EXIST);
        }

        try {
            // 保存回调记录
            saveSyncCallBack(kingStarCallbackDTO, callbackDTO);

            // 执行回调处理逻辑 - 使用重试机制查找申请单
            PriceApplyEntity priceApplyEntity = getPriceApplyWithRetry(callbackDTO.getInstructId());
            if (priceApplyEntity == null) {
                log.error("【KingStar】重试后仍未找到申请单信息，申请单号: {}", callbackDTO.getInstructId());
                return KingStarResponseDTO.failure(ResultCodeEnum.KINGSTAR_REQUEST_DATA_NOT_EXIST);
            }

            processCallback(priceApplyEntity, callbackDTO);
        } catch (Exception e) {
            log.error("【KingStar】回调处理异常，instructId: {}, 错误信息: {}", callbackDTO.getInstructId(), e.getMessage(), e);
            return KingStarResponseDTO.failure(ResultCodeEnum.KINGSTAR_REQUEST_UNKNOWN_ERROR);
        }

        return KingStarResponseDTO.success();
    }

    /**
     * 处理KingStar回调逻辑
     *
     * @param callbackDTO KingStar回调DTO
     */
    private void processCallback(PriceApplyEntity priceApplyEntity, KingStarCallbackDTO callbackDTO) {
        // 校验申请单的状态，如果是终态则不更新
        if (Arrays.asList(
                PriceStatusEnum.WAIT_ALLOCATE.getValue(),
                PriceStatusEnum.NOT_TRANSACTION.getValue()
        ).contains(priceApplyEntity.getStatus())) {
            log.info("申请单状态为 {}，不需要更新，申请单号: {}", PriceStatusEnum.getByValue(priceApplyEntity.getStatus()).getDescription(), priceApplyEntity.getCode());
            return;
        }

        // 回调接口的校验
        boolean success = handleCallBackCheck(callbackDTO);
        if (!success) {
            log.info("【KingStar】回调指令已处理过，跳过重复处理，指令ID: {}", callbackDTO.getInstructId());
            return;
        }

        String instructStatusStr = callbackDTO.getInstructStatus();
        int instructStatus = -1;

        try {
            instructStatus = Integer.parseInt(instructStatusStr);
            int priceStatus = KingStarInstructStatusEnum.getPriceStatusByCode(instructStatusStr);
            priceApplyEntity.setStatus(priceStatus);
        } catch (Exception e) {
            log.error("处理KingStar回调状态异常，申请单号: {}, 错误信息: {}", callbackDTO.getInstructId(), e.getMessage());
        }

        // 未成交状态处理
        if (instructStatus == KingStarInstructStatusEnum.NOT_TRANSACTION.getCode()) {
            NotDealDTO notDealDTO = new NotDealDTO();
            notDealDTO.setId(priceApplyEntity.getId());
            notDealDTO.setCancelReason("KingStar回调" + KingStarInterfaceStatusEnum.getByValue(priceApplyEntity.getInterfaceStatus()).getDescription() + "未成交");
            priceApplyService.batchNotDeal(Collections.singletonList(notDealDTO));

            priceApplyEntity.setInterfaceStatus(KingStarInterfaceStatusEnum.COMPLETED.getValue());
        }

        // 已成交状态处理
        if (Arrays.asList(
                KingStarInstructStatusEnum.TRADED_PENDING_ALLOCATION.getCode(),
                KingStarInstructStatusEnum.PARTIALLY_TRADED.getCode()
        ).contains(instructStatus)) {

            List<KingStarCallbackDTO.InstructStk> instructStkList = callbackDTO.getInstructStkList();
            if (CollectionUtil.isNotEmpty(instructStkList)) {

                int totalDealAmt = 0;
                int totalUnDealAmt = 0;
                BigDecimal totalDealAmount = BigDecimal.ZERO; // 成交总金额 = Σ(成交价 × 成交手数)

                for (KingStarCallbackDTO.InstructStk instructStk : instructStkList) {
                    log.info("合约: {}, 总手数: {}, 已成手数: {}, 未成手数: {}, 成交价格: {}",
                            instructStk.getStkCode(), instructStk.getAmt(), instructStk.getDealAmt(),
                            instructStk.getUnDealAmt(), instructStk.getPrice());

                    // 累加成交手数和成交金额
                    if (instructStk.getDealAmt() != null && instructStk.getPrice() != null) {
                        totalDealAmt += instructStk.getDealAmt();
                        totalDealAmount = totalDealAmount.add(
                                instructStk.getPrice().multiply(BigDecimal.valueOf(instructStk.getDealAmt()))
                        );
                    }

                    if (instructStk.getUnDealAmt() != null) {
                        totalUnDealAmt += instructStk.getUnDealAmt();
                    }
                }

                // 计算每手对应数量
                BigDecimal numPerHand = priceApplyEntity.getApplyNum()
                        .divide(priceApplyEntity.getApplyHandNum(), 6, RoundingMode.HALF_UP);

                // 全部成交
                BigDecimal dealNum = BigDecimalUtil.isEqual(priceApplyEntity.getApplyHandNum(), BigDecimal.valueOf(totalDealAmt))
                        ? priceApplyEntity.getApplyNum()
                        : BigDecimal.valueOf(totalDealAmt).multiply(numPerHand).setScale(2, RoundingMode.HALF_UP);

                BigDecimal notDealNum = BigDecimalUtil.isEqual(priceApplyEntity.getApplyHandNum(), BigDecimal.valueOf(totalDealAmt))
                        ? BigDecimal.ZERO
                        : BigDecimal.valueOf(totalUnDealAmt).multiply(numPerHand).setScale(2, RoundingMode.HALF_UP);

                // 计算加权平均价格
                BigDecimal avgPrice = BigDecimal.ZERO;
                if (totalDealAmt > 0) {
                    avgPrice = totalDealAmount
                            .divide(BigDecimal.valueOf(totalDealAmt), 4, RoundingMode.HALF_UP);
                }

                PriceApplyDTO priceApplyDTO = new PriceApplyDTO();
                priceApplyDTO.setId(priceApplyEntity.getId())
                        .setDealHandNum(totalDealAmt)
                        .setDealNum(dealNum)
                        .setNotDealNum(notDealNum);

                if (PriceTypeEnum.TRANSFER_MONTH.getValue() == priceApplyEntity.getType()) {
                    priceApplyDTO.setTransactionDiffPrice(avgPrice);
                } else {
                    priceApplyDTO.setTransactionPrice(avgPrice);
                }

                // 成交接口
                priceApplyService.priceApplyDealNew(priceApplyDTO);
            }

            priceApplyEntity.setInterfaceStatus(KingStarInterfaceStatusEnum.COMPLETED.getValue());
        }

        priceApplyEntity
                .setHmsInstruct(callbackDTO.getHmsInstruct())
                .setUpdatedAt(new Date());
        priceApplyDao.updateById(priceApplyEntity);
    }

    /**
     * 使用重试机制查找申请单
     * 解决KingStar回调与申请单创建同时到达导致的数据库事务未提交问题
     *
     * @param instructId 申请单号
     * @return 申请单实体，如果重试后仍未找到则返回null
     */
    private PriceApplyEntity getPriceApplyWithRetry(String instructId) {
        final int maxRetries = 5;
        final int baseDelayMs = 50;

        long startTime = System.currentTimeMillis();
        PriceApplyEntity priceApplyEntity = null;

        for (int attempt = 1; attempt <= maxRetries; attempt++) {
            log.info("【KingStar】第{}次查询申请单，申请单号: {}", attempt, instructId);

            priceApplyEntity = priceApplyDao.getPriceApplyByCode(instructId);

            if (priceApplyEntity != null) {
                long totalTime = System.currentTimeMillis() - startTime;
                log.info("【KingStar】第{}次查询成功找到申请单，申请单号: {}, 申请单ID: {}, 申请单状态: {}, 总耗时: {}ms",
                        attempt, instructId, priceApplyEntity.getId(), priceApplyEntity.getStatus(), totalTime);
                break;
            }

            // 如果不是最后一次尝试，则等待后重试
            if (attempt < maxRetries) {
                int delayMs = baseDelayMs * attempt; // 递增延迟：50ms, 100ms, 150ms, 200ms, 250ms
                log.warn("【KingStar】第{}次查询未找到申请单，{}ms后重试，申请单号: {}",
                        attempt, delayMs, instructId);

                try {
                    Thread.sleep(delayMs);
                } catch (InterruptedException e) {
                    log.error("【KingStar】重试等待被中断，申请单号: {}", instructId);
                    Thread.currentThread().interrupt();
                    break;
                }
            } else {
                long totalTime = System.currentTimeMillis() - startTime;
                log.error("【KingStar】重试{}次后仍未找到申请单，申请单号: {}, 总耗时: {}ms", maxRetries, instructId, totalTime);
            }
        }

        return priceApplyEntity;
    }

    /**
     * 回调接口的校验
     *
     * @param callbackDTO KingStar回调DTO
     */
    private boolean handleCallBackCheck(KingStarCallbackDTO callbackDTO) {
        String hmsInstruct = callbackDTO.getHmsInstruct();
        String instructId = callbackDTO.getInstructId();
        String instructStatus = callbackDTO.getInstructStatus();
        String redisKey = "callback:instruct:" + hmsInstruct + ":" + instructId + ":" + instructStatus;

        // 使用原子操作设置 Redis Key（避免并发时重复处理）
        boolean isFirst = redisUtil.setNx(redisKey, 10L);
        if (!isFirst) {
            log.info("【KingStar】重复回调拦截，hms系统指令ID: {}, 指令ID: {}, 状态: {}", hmsInstruct, instructId, instructStatus);
            return false;
        }

        log.info("【KingStar】首次处理回调，记录已写入 Redis，hms系统指令ID: {}, 指令ID: {}, 状态: {}", hmsInstruct, instructId, instructStatus);
        return true;
    }

    /**
     * 保存同步回调记录
     *
     * @param kingStarCallbackDTO KingStar回调DTO字符串
     * @param callbackDTO         KingStar回调DTO
     */
    private void saveSyncCallBack(String kingStarCallbackDTO, KingStarCallbackDTO callbackDTO) {
        KingStarSyncCallbackEntity syncCallbackEntity = new KingStarSyncCallbackEntity();
        syncCallbackEntity.setInstructId(callbackDTO.getInstructId())
                .setInstructStatus(Integer.parseInt(callbackDTO.getInstructStatus()))
                .setAckData(kingStarCallbackDTO)
                .setCreatedAt(new Date())
                .setUpdatedAt(new Date());
        kingStarSyncCallbackDao.save(syncCallbackEntity);
    }

    /**
     * 根据用户的code获取token
     *
     * @param userCode     用户code
     * @param forceRefresh 是否强制刷新token
     * @return token
     */
    @Override
    public String getTokenByUserCode(String userCode, boolean forceRefresh) {
        // 生成redis的key
        String kingStarKey = "kingstar:token:" + userCode;
        // token有效时间
        long VALID_TIME_MILLIS = 10 * 60L;

        // 非强制刷新情况下尝试从 Redis 获取
        if (!forceRefresh) {
            String token = redisUtil.getString(kingStarKey);
            if (StringUtils.isNotBlank(token)) {
                log.info("从Redis中获取到token，userCode: {}, token: {}", userCode, token);
                return token;
            }
        } else {
            log.info("强制刷新token，userCode: {}", userCode);
        }

        // 根据userCode获取licenseCode
        String licenseCode = AESUtil.getLicenseCode(userCode);

        if (StringUtils.isBlank(licenseCode)) {
            log.error("获取licenseCode失败，userCode: {}", userCode);
            return null;
        }

        // 获取并刷新token
        String token = syncUriService.getTokenByLicense(licenseCode);
        if (StringUtils.isNotBlank(token)) {
            redisUtil.set(kingStarKey, token, VALID_TIME_MILLIS);
        }

        return token;
    }

    @Override
    public void reSyncByRequestId(Integer requestId) throws URISyntaxException {
        // 查询同步请求记录
        KingStarSyncRequestEntity syncRequest = kingStarSyncRequestDao.getById(requestId);
        if (syncRequest == null) {
            return;
        }

        // 更新同步请求记录的重试信息
        syncRequest.setTryTimes(syncRequest.getTryTimes() + 1)
                .setSyncTime(DateUtil.format(new Date(), "yyyyMMddHHmmss"))
                .setSyncStatus("重试中");

        // 获取 Token
        String token = getTokenByUserCode(syncRequest.getUserCode(), false);

        // 构造请求参数
        URI uri = new URI(syncRequest.getRequestUrl());
        KingStarBusinessTypeEnum businessTypeEnum = KingStarBusinessTypeEnum.getByValue(syncRequest.getOperationType());
        KingStarRequestDTO requestDTO = BeanUtil.toBean(syncRequest.getRequestInfo(), KingStarRequestDTO.class);

        // 调用接口
        String responseDTO = "";
        String resultMessage = "";
        try {
            KingStarResponseDTO kingStarResponseDTO = invokeRemoteInterface(businessTypeEnum, uri, token, requestDTO);
            resultMessage = (kingStarResponseDTO == null) ? "接口调用失败" : kingStarResponseDTO.getMessage();
            responseDTO = JSONUtil.toJsonStr(responseDTO);

        } catch (Exception e) {
            resultMessage = "接口调用失败";
            responseDTO = "重新同步请求失败，" + e.getMessage();
            log.error("重新同步请求失败，requestId: {}, 错误信息: {}", requestId, e.getMessage(), e);
            throw new RuntimeException(e.getMessage());
        } finally {
            // 更新申请单及同步记录 - 使用重试机制查找申请单
            PriceApplyEntity priceApplyEntity = getPriceApplyWithRetry(syncRequest.getInstructId());
            if (priceApplyEntity != null) {
                updatePriceApplyEntity(priceApplyEntity, resultMessage);
            } else {
                log.warn("【KingStar】重新同步时未找到申请单，申请单号: {}", syncRequest.getInstructId());
            }

            syncRequest.setSyncStatus(resultMessage)
                    .setResponseInfo(responseDTO);
            kingStarSyncRequestDao.updateById(syncRequest);
        }
    }

    // 调用远程接口
    private KingStarResponseDTO invokeRemoteInterface(KingStarBusinessTypeEnum type, URI uri, String token, KingStarRequestDTO requestDTO) {
        switch (type) {
            case PRICING:
            case PRICING_TRANSFER:
                return futureRemoteFacade.applyPricing(uri, token, requestDTO);
            case REVERSE_PRICING:
            case REVERSE_PRICING_TRANSFER:
                return futureRemoteFacade.applyReversePricing(uri, token, requestDTO);
            case MODIFY:
                return futureRemoteFacade.applyModify(uri, token, requestDTO);
            case CANCEL:
                return futureRemoteFacade.applyCancel(uri, token, requestDTO);
            default:
                return null;
        }
    }

    /**
     * 获取KingStar的合约代码
     *
     * @param futureCode 品种代码
     * @param domainCode 合约代码
     * @return KingStar合约代码
     */
    private String getKingStarDomainCode(String futureCode, String domainCode) {
        if (StringUtils.isAnyBlank(futureCode, domainCode)) {
            return "";
        }
        return ("OI".equals(futureCode) || "RM".equals(futureCode))
                ? futureCode + domainCode.substring(1)
                : futureCode.toLowerCase() + domainCode;
    }

    /**
     * 获取KingStar的合约前缀
     *
     * @param futureCode 品种代码
     * @return KingStar合约前缀
     */
    private String getStkFutureCode(String futureCode) {
        if (StringUtils.isBlank(futureCode)) {
            return "";
        }
        return "OI".equals(futureCode) || "RM".equals(futureCode) ? "SPD" : "SP";
    }

}